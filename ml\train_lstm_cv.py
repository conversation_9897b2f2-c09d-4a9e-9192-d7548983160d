# © 2024 <PERSON> <<EMAIL>>
# All rights reserved.
# This code is licensed under the MIT License. See LICENSE file for details.

"""
5-Fold Cross-Validation training for CBOW-LSTM model.
Provides more stable performance metrics with limited data.
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Subset
import logging
import os
import sys
from typing import List, Dict
from sklearn.model_selection import KFold
from sklearn.metrics import accuracy_score, roc_auc_score, log_loss
from gensim.models import KeyedVectors

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ml.create_model_lstm import CBOWLSTMModel, DraftSequenceDataset, load_data, load_embeddings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Set device
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
logger.info(f"Using device: {device}")

def train_fold(model: CBOWLSTMModel, train_loader: DataLoader, val_loader: DataLoader, 
               fold: int, num_epochs: int = 100, learning_rate: float = 1e-4) -> Dict:
    """Train model for one fold."""
    
    criterion = nn.BCELoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=1e-5)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=15, factor=0.5, min_lr=1e-6)
    
    best_val_loss = float('inf')
    patience_counter = 0
    patience = 20
    
    model.to(device)
    
    for epoch in range(num_epochs):
        # Training phase
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        for hero_sequence, position_sequence, labels in train_loader:
            hero_sequence = hero_sequence.to(device)
            position_sequence = position_sequence.to(device)
            labels = labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(hero_sequence, position_sequence)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            predicted = (outputs > 0.5).float()
            train_total += labels.size(0)
            train_correct += (predicted == labels).sum().item()
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            for hero_sequence, position_sequence, labels in val_loader:
                hero_sequence = hero_sequence.to(device)
                position_sequence = position_sequence.to(device)
                labels = labels.to(device)
                
                outputs = model(hero_sequence, position_sequence)
                loss = criterion(outputs, labels)
                
                val_loss += loss.item()
                predicted = (outputs > 0.5).float()
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()
        
        # Calculate metrics
        train_acc = train_correct / train_total
        val_acc = val_correct / val_total
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(val_loader)
        
        # Learning rate scheduling
        scheduler.step(avg_val_loss)
        
        # Early stopping
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            patience_counter = 0
            best_val_acc = val_acc
        else:
            patience_counter += 1
        
        # Log progress occasionally
        if epoch % 20 == 0 or epoch < 10:
            logger.info(f"Fold {fold+1}, Epoch {epoch+1}: "
                       f"Train Acc: {train_acc:.4f}, Val Acc: {val_acc:.4f}")
        
        # Early stopping
        if patience_counter >= patience:
            logger.info(f"Fold {fold+1}: Early stopping at epoch {epoch+1}")
            break
    
    return {
        'best_val_loss': best_val_loss,
        'best_val_acc': best_val_acc,
        'final_train_acc': train_acc
    }

def evaluate_fold(model: CBOWLSTMModel, test_loader: DataLoader) -> Dict:
    """Evaluate model on test set."""
    model.eval()
    all_predictions = []
    all_probabilities = []
    all_labels = []
    
    with torch.no_grad():
        for hero_sequence, position_sequence, labels in test_loader:
            hero_sequence = hero_sequence.to(device)
            position_sequence = position_sequence.to(device)
            labels = labels.to(device)
            
            outputs = model(hero_sequence, position_sequence)
            probabilities = outputs.cpu().numpy().flatten()
            predictions = (outputs > 0.5).float().cpu().numpy().flatten()
            labels_np = labels.cpu().numpy().flatten()
            
            all_predictions.extend(predictions)
            all_probabilities.extend(probabilities)
            all_labels.extend(labels_np)
    
    # Calculate metrics
    accuracy = accuracy_score(all_labels, all_predictions)
    try:
        auc = roc_auc_score(all_labels, all_probabilities)
    except ValueError:
        auc = 0.5  # If only one class present
    
    try:
        logloss = log_loss(all_labels, all_probabilities)
    except ValueError:
        logloss = float('inf')
    
    return {
        'accuracy': accuracy,
        'roc_auc': auc,
        'log_loss': logloss
    }

def cross_validate_lstm():
    """Perform 5-fold cross-validation on LSTM model."""
    logger.info("Starting 5-fold cross-validation for CBOW-LSTM model")
    
    # Load data
    sequences, labels = load_data()
    embeddings = load_embeddings()
    
    # Create full dataset
    full_dataset = DraftSequenceDataset(sequences, labels, embeddings)
    
    # 5-fold cross-validation
    kfold = KFold(n_splits=5, shuffle=True, random_state=42)
    fold_results = []
    
    for fold, (train_idx, val_idx) in enumerate(kfold.split(sequences)):
        logger.info(f"\n--- Fold {fold+1}/5 ---")
        
        # Create data loaders for this fold
        train_subset = Subset(full_dataset, train_idx)
        val_subset = Subset(full_dataset, val_idx)
        
        train_loader = DataLoader(train_subset, batch_size=32, shuffle=True)
        val_loader = DataLoader(val_subset, batch_size=32, shuffle=False)
        
        logger.info(f"Train samples: {len(train_idx)}, Val samples: {len(val_idx)}")
        
        # Create fresh model for this fold
        model = CBOWLSTMModel(embedding_dim=150, lstm_hidden_size=128, dropout_rate=0.15)
        
        # Train model
        train_results = train_fold(model, train_loader, val_loader, fold)
        
        # Evaluate model
        eval_results = evaluate_fold(model, val_loader)
        
        # Combine results
        fold_result = {
            'fold': fold + 1,
            'train_acc': train_results['final_train_acc'],
            'val_acc': eval_results['accuracy'],
            'val_auc': eval_results['roc_auc'],
            'val_loss': eval_results['log_loss']
        }
        
        fold_results.append(fold_result)
        
        logger.info(f"Fold {fold+1} Results:")
        logger.info(f"  Train Accuracy: {fold_result['train_acc']:.4f}")
        logger.info(f"  Val Accuracy: {fold_result['val_acc']:.4f}")
        logger.info(f"  Val AUC: {fold_result['val_auc']:.4f}")
        logger.info(f"  Val Log Loss: {fold_result['val_loss']:.4f}")
    
    # Calculate overall statistics
    val_accuracies = [r['val_acc'] for r in fold_results]
    val_aucs = [r['val_auc'] for r in fold_results]
    train_accuracies = [r['train_acc'] for r in fold_results]
    
    mean_val_acc = np.mean(val_accuracies)
    std_val_acc = np.std(val_accuracies)
    mean_val_auc = np.mean(val_aucs)
    std_val_auc = np.std(val_aucs)
    mean_train_acc = np.mean(train_accuracies)
    
    # Print final results
    logger.info("\n" + "="*60)
    logger.info("5-FOLD CROSS-VALIDATION RESULTS")
    logger.info("="*60)
    logger.info(f"Mean Validation Accuracy: {mean_val_acc:.4f} ± {std_val_acc:.4f}")
    logger.info(f"Mean Validation AUC: {mean_val_auc:.4f} ± {std_val_auc:.4f}")
    logger.info(f"Mean Training Accuracy: {mean_train_acc:.4f}")
    logger.info(f"Dataset Size: {len(sequences)} matches")
    
    # Individual fold results
    logger.info("\nIndividual Fold Results:")
    for result in fold_results:
        logger.info(f"Fold {result['fold']}: Val Acc = {result['val_acc']:.4f}, "
                   f"Train Acc = {result['train_acc']:.4f}")
    
    # Assessment
    if mean_val_acc >= 0.70:
        logger.info("✅ Model meets the minimum 70% accuracy requirement")
    elif mean_val_acc >= 0.60:
        logger.info("⚠️ Model shows promise but needs more data or tuning")
    else:
        logger.info("❌ Model performance is poor. Need significantly more data.")
    
    # Check for overfitting
    overfitting_gap = mean_train_acc - mean_val_acc
    if overfitting_gap > 0.2:
        logger.info(f"⚠️ Significant overfitting detected (gap: {overfitting_gap:.4f})")
    elif overfitting_gap > 0.1:
        logger.info(f"⚠️ Some overfitting detected (gap: {overfitting_gap:.4f})")
    else:
        logger.info(f"✅ Good generalization (gap: {overfitting_gap:.4f})")
    
    return {
        'mean_val_accuracy': mean_val_acc,
        'std_val_accuracy': std_val_acc,
        'mean_val_auc': mean_val_auc,
        'std_val_auc': std_val_auc,
        'mean_train_accuracy': mean_train_acc,
        'fold_results': fold_results,
        'dataset_size': len(sequences)
    }

if __name__ == "__main__":
    try:
        results = cross_validate_lstm()
        logger.info("🎉 Cross-validation completed successfully!")
    except Exception as e:
        logger.error(f"❌ Cross-validation failed: {e}")
        raise
