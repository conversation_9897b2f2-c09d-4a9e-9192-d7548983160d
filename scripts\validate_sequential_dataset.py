# © 2024 <PERSON> <<EMAIL>>
# All rights reserved.
# This code is licensed under the MIT License. See LICENSE file for details.

"""
Validate the sequential draft dataset format for LSTM training.
"""

import pandas as pd
import logging
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def validate_sequential_dataset():
    """Validate the sequential drafts dataset format and content."""
    logger.info("Starting sequential dataset validation")
    
    # Load dataset
    try:
        df = pd.read_csv("dataset/train_data/sequential_drafts.csv")
        logger.info(f"Loaded sequential dataset with {len(df)} matches")
    except FileNotFoundError:
        logger.error("Sequential drafts dataset not found")
        return False
    except Exception as e:
        logger.error(f"Error loading dataset: {e}")
        return False
    
    # Validate required columns
    required_columns = ['match_id', 'pick_sequence', 'radiant_win']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        logger.error(f"Missing required columns: {missing_columns}")
        return False
    
    logger.info("✅ All required columns present")
    
    # Validate data types
    if not pd.api.types.is_integer_dtype(df['match_id']):
        logger.error("match_id column should be integer type")
        return False
    
    if not pd.api.types.is_integer_dtype(df['radiant_win']):
        logger.error("radiant_win column should be integer type")
        return False
    
    logger.info("✅ Data types are correct")
    
    # Validate radiant_win values
    unique_wins = df['radiant_win'].unique()
    if not all(val in [0, 1] for val in unique_wins):
        logger.error(f"radiant_win should only contain 0 or 1, found: {unique_wins}")
        return False
    
    logger.info("✅ radiant_win values are valid")
    
    # Validate pick sequences
    invalid_sequences = []
    for idx, row in df.iterrows():
        pick_sequence = row['pick_sequence']
        
        # Check if it's a string
        if not isinstance(pick_sequence, str):
            invalid_sequences.append(f"Row {idx}: pick_sequence is not a string")
            continue
        
        # Parse the sequence
        try:
            hero_ids = [int(x.strip()) for x in pick_sequence.split(',')]
        except ValueError as e:
            invalid_sequences.append(f"Row {idx}: Cannot parse pick_sequence: {e}")
            continue
        
        # Check if we have exactly 10 picks
        if len(hero_ids) != 10:
            invalid_sequences.append(f"Row {idx}: Expected 10 picks, got {len(hero_ids)}")
            continue
        
        # Check if all hero IDs are positive integers
        if any(hero_id <= 0 for hero_id in hero_ids):
            invalid_sequences.append(f"Row {idx}: Invalid hero IDs (should be positive): {hero_ids}")
            continue
    
    if invalid_sequences:
        logger.error(f"Found {len(invalid_sequences)} invalid pick sequences:")
        for error in invalid_sequences[:5]:  # Show first 5 errors
            logger.error(f"  {error}")
        if len(invalid_sequences) > 5:
            logger.error(f"  ... and {len(invalid_sequences) - 5} more")
        return False
    
    logger.info("✅ All pick sequences are valid")
    
    # Validate no duplicate match IDs
    duplicate_matches = df[df.duplicated(subset=['match_id'], keep=False)]
    if not duplicate_matches.empty:
        logger.error(f"Found {len(duplicate_matches)} duplicate match IDs")
        return False
    
    logger.info("✅ No duplicate match IDs")
    
    # Show sample data
    logger.info("Sample data:")
    for idx, row in df.head(3).iterrows():
        hero_ids = [int(x.strip()) for x in row['pick_sequence'].split(',')]
        logger.info(f"  Match {row['match_id']}: {hero_ids} -> Radiant Win: {row['radiant_win']}")
    
    # Show statistics
    win_distribution = df['radiant_win'].value_counts()
    logger.info(f"Win distribution: Dire wins: {win_distribution.get(0, 0)}, Radiant wins: {win_distribution.get(1, 0)}")
    
    radiant_win_rate = df['radiant_win'].mean()
    logger.info(f"Radiant win rate: {radiant_win_rate:.3f}")
    
    logger.info("🎉 Sequential dataset validation completed successfully!")
    return True

if __name__ == "__main__":
    try:
        success = validate_sequential_dataset()
        if not success:
            sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Validation failed: {e}")
        sys.exit(1)
