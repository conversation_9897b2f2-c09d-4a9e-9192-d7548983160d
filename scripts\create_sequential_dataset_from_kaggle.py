import pandas as pd
import numpy as np
import os
from pathlib import Path
import logging
from collections import Counter

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Define paths
KAGGLE_DATA_DIR = Path("dataset/kaggle_data")
CONSTANTS_PATH = KAGGLE_DATA_DIR / "Constants" / "Constants.Heroes.csv"
COUNTER_RATES_PATH = Path('models') / 'hero_counter_rates.csv'
OUTPUT_DIR = Path('dataset/train_data')
OUTPUT_PATH = OUTPUT_DIR / 'sequential_drafts_with_features.csv'

# Specify which months to include
MONTHS = ["202501", "202502", "202503", "202504", "202505", "202506", "202507"]

def load_data():
    """Loads all monthly picks/bans and metadata from Kaggle CSVs."""
    picks_bans_dfs = []
    meta_dfs = []
    for month in MONTHS:
        month_dir = KAGGLE_DATA_DIR / month
        picks_bans_file = month_dir / "picks_bans.csv"
        meta_file = month_dir / "main_metadata.csv"
        
        if picks_bans_file.exists() and meta_file.exists():
            logging.info(f"Loading data for month: {month}")
            try:
                picks_bans_dfs.append(pd.read_csv(picks_bans_file))
                meta_dfs.append(pd.read_csv(meta_file, usecols=["match_id", "radiant_win"]))
            except Exception as e:
                logging.error(f"Could not read CSVs in {month}: {e}")
        else:
            logging.warning(f"Warning: missing data for month {month}")
            
    if not picks_bans_dfs or not meta_dfs:
        return None, None

    picks_bans_df = pd.concat(picks_bans_dfs, ignore_index=True)
    meta_df = pd.concat(meta_dfs, ignore_index=True).drop_duplicates(subset=["match_id"])
    return picks_bans_df, meta_df

def load_hero_features():
    """Loads hero features from Constants.Heroes.csv."""
    if not CONSTANTS_PATH.exists():
        logging.error(f"Hero constants file not found at: {CONSTANTS_PATH}")
        return None
    
    heroes_df = pd.read_csv(CONSTANTS_PATH, usecols=['id', 'roles', 'primary_attr', 'attack_type'])
    heroes_df = heroes_df.rename(columns={'id': 'hero_id'})
    
    # Safely evaluate roles string
    def parse_roles(roles_str):
        try:
            return eval(roles_str)
        except:
            return []
    heroes_df['roles'] = heroes_df['roles'].apply(parse_roles)
    
    return heroes_df

def aggregate_hero_features(pick_sequence, hero_features_map):
    """Aggregates features for a sequence of hero IDs."""
    roles = Counter()
    primary_attrs = Counter()
    attack_types = Counter()
    
    for hero_id in pick_sequence:
        features = hero_features_map.get(hero_id)
        if features:
            roles.update(features['roles'])
            primary_attrs.update([features['primary_attr']])
            attack_types.update([features['attack_type']])
            
    return {
        "roles": dict(roles),
        "primary_attrs": dict(primary_attrs),
        "attack_types": dict(attack_types)
    }

def main():
    """Main function to generate the sequential draft dataset with features."""
    logging.info("Starting dataset generation...")
    
    picks_bans, meta = load_data()
    if picks_bans is None:
        logging.error("No picks/bans data loaded. Aborting.")
        return

    hero_features = load_hero_features()
    if hero_features is None:
        logging.error("No hero features loaded. Aborting.")
        return
        
    hero_features_map = hero_features.set_index('hero_id').to_dict('index')

    # 1. Filter for picks only
    picks = picks_bans[picks_bans['is_pick']].copy()

    # 2. Group by match and team to create pick sequences
    sequences = (
        picks.sort_values(['match_id', 'order'])
             .groupby(['match_id', 'team'])['hero_id']
             .apply(list)
             .unstack(level='team')
    )
    sequences.columns = ['radiant_picks', 'dire_picks']
    sequences = sequences.reset_index()

    # 3. Validate 5 picks per team and format sequences
    sequences = sequences.dropna(subset=['radiant_picks', 'dire_picks'])
    sequences = sequences[
        (sequences['radiant_picks'].apply(len) == 5) & 
        (sequences['dire_picks'].apply(len) == 5)
    ]
    
    sequences['radiant_pick_sequence'] = sequences['radiant_picks'].apply(lambda ids: ','.join(map(str, ids)))
    sequences['dire_pick_sequence'] = sequences['dire_picks'].apply(lambda ids: ','.join(map(str, ids)))

    # 4. Merge with match outcome
    result = pd.merge(
        sequences[['match_id', 'radiant_pick_sequence', 'dire_pick_sequence', 'radiant_picks', 'dire_picks']],
        meta,
        on='match_id',
        how='inner'
    )

    # 5. Aggregate and add hero features
    logging.info("Aggregating hero features for each team...")
    radiant_features = result['radiant_picks'].apply(lambda x: aggregate_hero_features(x, hero_features_map))
    dire_features = result['dire_picks'].apply(lambda x: aggregate_hero_features(x, hero_features_map))

    result['radiant_roles'] = radiant_features.apply(lambda x: x['roles'])
    result['radiant_primary_attrs'] = radiant_features.apply(lambda x: x['primary_attrs'])
    result['radiant_attack_types'] = radiant_features.apply(lambda x: x['attack_types'])
    
    result['dire_roles'] = dire_features.apply(lambda x: x['roles'])
    result['dire_primary_attrs'] = dire_features.apply(lambda x: x['primary_attrs'])
    result['dire_attack_types'] = dire_features.apply(lambda x: x['attack_types'])

    # 6. Add counter-rate features
    logging.info("Loading and applying hero counter-rates...")
    if not COUNTER_RATES_PATH.exists():
        logging.error(f"Counter rates file not found at {COUNTER_RATES_PATH}. Skipping this feature.")
    else:
        counter_rates_df = pd.read_csv(COUNTER_RATES_PATH, index_col=0)
        counter_rates_df.columns = counter_rates_df.columns.astype(int) # Ensure columns are int for lookup
        
        def get_avg_counter_rates(team_picks, opponent_picks):
            avg_rates = []
            for hero_id in team_picks:
                rates = [counter_rates_df.loc[hero_id, opp_id] for opp_id in opponent_picks if opp_id in counter_rates_df.columns and hero_id in counter_rates_df.index]
                avg_rates.append(np.mean(rates) if rates else 0.5)
            return avg_rates

        result['radiant_counter_rates'] = result.apply(lambda row: get_avg_counter_rates(row['radiant_picks'], row['dire_picks']), axis=1)
        result['dire_counter_rates'] = result.apply(lambda row: get_avg_counter_rates(row['dire_picks'], row['radiant_picks']), axis=1)

    # 7. Clean up and save
    final_cols = [
        'match_id', 
        'radiant_pick_sequence', 
        'dire_pick_sequence', 
        'radiant_win',
        'radiant_roles',
        'radiant_primary_attrs',
        'radiant_attack_types',
        'dire_roles',
        'dire_primary_attrs',
        'dire_attack_types',
        'radiant_counter_rates',
        'dire_counter_rates'
    ]
    # Filter to only include columns that actually exist, in case counter-rates failed
    result = result[[col for col in final_cols if col in result.columns]]

    OUTPUT_DIR.mkdir(exist_ok=True)
    result.to_csv(OUTPUT_PATH, index=False)
    
    logging.info(f"Successfully generated dataset with {len(result)} matches.")
    logging.info(f"Dataset saved to {OUTPUT_PATH}")

if __name__ == '__main__':
    main()
